import{u as d,b as p,r as s,j as t,m as n,A as u}from"./index-DmbGcuU4.js";const g=()=>{const{slug:a}=d(),e=p.find(o=>o.slug===a),[l,i]=s.useState(!1);s.useEffect(()=>{const o=()=>{const c=window.scrollY||document.documentElement.scrollTop,x=document.documentElement.scrollHeight,m=window.innerHeight;i(c+m>=x*.75)};return window.addEventListener("scroll",o),()=>window.removeEventListener("scroll",o)},[]);const r=()=>{window.scrollTo({top:0,behavior:"smooth"})};return e?t.jsxs(t.Fragment,{children:[t.jsxs(n.main,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:.5,ease:"easeOut"},className:"py-20 px-6 max-w-7xl mx-auto relative",children:[t.jsx("p",{className:"text-primary text-lg mb-4 text-center",children:e.date}),t.jsx("h1",{className:"text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-serif font-bold mb-10 text-center",children:e.title}),t.jsx("div",{className:"prose dark:prose-invert max-w-none text-[1.20rem] leading-relaxed",dangerouslySetInnerHTML:{__html:e.content}})]}),t.jsx(u,{children:l&&t.jsx(n.button,{initial:{opacity:0,scale:.8,y:40},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.8,y:40},transition:{duration:.3,ease:"easeOut"},onClick:r,className:"fixed bottom-20 right-40 bg-primary text-white px-8 py-3 rounded-lg shadow-lg hover:bg-primary/80 transition",children:"↑ Back to Top"})})]}):t.jsx("div",{className:"py-20 px-6 max-w-5xl mx-auto",children:t.jsx("h1",{className:"text-3xl font-bold",children:"Post not found"})})};export{g as default};
