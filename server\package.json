{"name": "brayden-portfolio-backend", "version": "1.0.0", "description": "Backend API for Brayden's portfolio website", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["portfolio", "api", "express", "node"], "author": "<PERSON><PERSON>y", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "sqlite3": "^5.1.6", "express-rate-limit": "^7.1.5"}, "devDependencies": {"nodemon": "^3.0.2"}}