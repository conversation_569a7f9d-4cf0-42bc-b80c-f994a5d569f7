import{r as u,j as t,c as N,t as L,d as A,e as D,f as P,g as M,h as B,i as $,u as R,p as _,L as C,m as E,A as H}from"./index-DmbGcuU4.js";const k={MONTE_CARLO:"PythonMonteCarloBasic",BOND_TRACKER:"BondTracker",MANDELBROT:"mandelbrot-excel",BOEING:"boeing"},O={[k.MONTE_CARLO]:127,[k.BOND_TRACKER]:89,[k.MANDELBROT]:64,[k.BOEING]:43},V=r=>{const e=localStorage.getItem(`viewCount_${r}`);return e?parseInt(e,10):O[r]||0},F=(r,e)=>{localStorage.setItem(`viewCount_${r}`,e.toString()),console.log(`✅ Set view count for ${r} to ${e}`)},U=()=>{const[r,e]=u.useState(0);return u.useEffect(()=>{const m=()=>{const i=window.scrollY,x=document.documentElement.scrollHeight-window.innerHeight,a=i/x*100;e(Math.min(a,100))};return window.addEventListener("scroll",m),()=>window.removeEventListener("scroll",m)},[]),t.jsx("div",{className:"fixed top-0 left-0 w-full h-1 bg-gray-200 dark:bg-gray-800 z-40 pointer-events-none",children:t.jsx("div",{className:"h-full bg-gradient-to-r from-primary to-primary/80 transition-all duration-150 ease-out",style:{width:`${r}%`}})})},z="http://localhost:3001/api";class I{constructor(e=z){this.baseUrl=e}async getViewCount(e){return null}async recordPageView(e){return null}async getAnalyticsSummary(){try{const e=await fetch(`${this.baseUrl}/analytics/summary`);if(!e.ok)throw new Error("Failed to fetch analytics summary");return await e.json()}catch(e){return console.warn("Backend not available:",e),null}}async getReactions(e){return null}async addReaction(e,m){return null}async removeReaction(e,m){try{const i=await fetch(`${this.baseUrl}/reactions/${e}/${m}`,{method:"DELETE",headers:{"Content-Type":"application/json"}});if(!i.ok)throw new Error("Failed to remove reaction");return await i.json()}catch(i){return console.warn("Backend not available, reaction not removed:",i),null}}async checkHealth(){try{return(await fetch(`${this.baseUrl}/health`)).ok}catch{return!1}}}const T=new I,Y=({projectId:r,className:e="",recordView:m=!1})=>{const[i,x]=u.useState(0),[a,p]=u.useState(!1),w={PythonMonteCarloBasic:127,BondTracker:89,"mandelbrot-excel":64,boeing:43},c=f=>{const n=localStorage.getItem(`viewCount_${f}`);if(n)return parseInt(n,10);const s=w[f]||Math.floor(Math.random()*60)+15;return localStorage.setItem(`viewCount_${f}`,s.toString()),s},g=f=>{const n=`lastIncrement_${f}`,s=localStorage.getItem(n),d=Date.now();if(!s)return localStorage.setItem(n,d.toString()),!1;const o=(d-parseInt(s,10))/(1e3*60*60*24),l=Math.random()*2+2;return o>=l?(localStorage.setItem(n,d.toString()),!0):!1},b=(f,n)=>{const s=Math.floor(Math.random()*5)+1,d=f+s;return localStorage.setItem(`viewCount_${n}`,d.toString()),d};return u.useEffect(()=>{(async()=>{try{const n=await T.getViewCount(r);if(n)x(n.view_count),p(!0),m&&await T.recordPageView(r);else{let s=c(r);g(r)&&(s=b(s,r)),x(s),p(!1)}}catch{let s=c(r);g(r)&&(s=b(s,r)),x(s),p(!1)}})()},[r,m]),t.jsxs("div",{className:`flex items-center text-sm text-muted-foreground ${e}`,children:[t.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:`mr-1 ${a?"text-green-500":""}`,children:[t.jsx("path",{d:"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"}),t.jsx("circle",{cx:"12",cy:"12",r:"3"})]}),"Viewed ",i," times",a&&t.jsx("span",{className:"ml-1 text-xs text-green-500",title:"Live data",children:"●"})]})},W=({date:r="6/29/25",className:e=""})=>t.jsxs("div",{className:`flex items-center text-sm text-muted-foreground ${e}`,children:[t.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"mr-1",children:[t.jsx("circle",{cx:"12",cy:"12",r:"10"}),t.jsx("polyline",{points:"12,6 12,12 16,14"})]}),"Last updated ",r]}),q=({sections:r,className:e=""})=>{const[m,i]=u.useState("");u.useEffect(()=>{const a=()=>{const p=window.scrollY+100;for(let w=r.length-1;w>=0;w--){const c=document.getElementById(r[w].id);if(c&&c.offsetTop<=p){i(r[w].id);break}}};return window.addEventListener("scroll",a),a(),()=>window.removeEventListener("scroll",a)},[r]);const x=a=>{const p=document.getElementById(a);if(p){const w=p.offsetTop-80;window.scrollTo({top:w,behavior:"smooth"})}};return r.length===0?null:t.jsx("div",{className:`hidden lg:block fixed right-4 top-1/2 transform -translate-y-1/2 w-64 z-30 ${e}`,children:t.jsxs("div",{className:"bg-card/90 backdrop-blur-sm border border-border rounded-lg p-4 shadow-lg",children:[t.jsx("h3",{className:"text-sm font-semibold text-foreground mb-3 border-b border-border pb-2",children:"Table of Contents"}),t.jsx("nav",{className:"space-y-1 max-h-96 overflow-y-auto",children:r.map(a=>t.jsx("button",{onClick:()=>x(a.id),className:`block w-full text-left text-sm py-2 px-2 rounded transition-colors ${m===a.id?"text-primary bg-primary/10 font-medium":"text-muted-foreground hover:text-foreground hover:bg-muted/50"} ${a.level>1?"ml-4":""}`,children:a.title},a.id))})]})})},G=({repo:r,repoId:e,category:m,categoryId:i,mapping:x="pathname",strict:a="0",reactionsEnabled:p="1",emitMetadata:w="0",inputPosition:c="bottom",theme:g="preferred_color_scheme",lang:b="en",loading:f="lazy"})=>{const n=u.useRef(null),[s,d]=u.useState(g);return u.useEffect(()=>{const o=()=>{const v=document.documentElement.classList.contains("dark"),h=g==="preferred_color_scheme"?v?"dark":"light":g;h!==s&&d(h)};o();const l=new MutationObserver(o);return l.observe(document.documentElement,{attributes:!0,attributeFilter:["class"]}),()=>l.disconnect()},[g,s]),u.useEffect(()=>{n.current&&(n.current.innerHTML="");const o=document.createElement("script");return o.src="https://giscus.app/client.js",o.setAttribute("data-repo",r),o.setAttribute("data-repo-id",e),o.setAttribute("data-category",m),o.setAttribute("data-category-id",i),o.setAttribute("data-mapping",x),o.setAttribute("data-strict",a),o.setAttribute("data-reactions-enabled",p),o.setAttribute("data-emit-metadata",w),o.setAttribute("data-input-position",c),o.setAttribute("data-theme",s),o.setAttribute("data-lang",b),o.setAttribute("data-loading",f),o.setAttribute("crossorigin","anonymous"),o.async=!0,n.current&&n.current.appendChild(o),()=>{n.current&&(n.current.innerHTML="")}},[r,e,m,i,x,a,p,w,c,s,b,f]),t.jsxs("div",{className:"giscus-comments mt-8 pt-8 border-t border-border",children:[t.jsx("h3",{className:"text-xl font-semibold mb-4 text-foreground",children:"Comments & Discussion"}),t.jsx("div",{ref:n,className:"giscus-container",style:{minHeight:"200px"}})]})},j=r=>{const e=N();u.useEffect(()=>{const c=e.pathname==="/"?"Homepage":e.pathname.includes("/projects")?"Project Page":e.pathname.includes("/blog")?"Blog":e.pathname.includes("/contact")?"Contact":"Unknown";L(c)},[e,r]);const m=u.useCallback((c={})=>{if(!r)return;A({projectId:r,viewDuration:c.viewDuration,scrollDepth:c.scrollDepth,referrer:document.referrer,userAgent:navigator.userAgent});const g=V(r);F(r,g+1)},[r]),i=u.useCallback(c=>{r&&(c.split(".").pop(),D())},[r]),x=u.useCallback(c=>{r&&P()},[r]),a=u.useCallback(c=>{r&&M()},[r]),p=u.useCallback(c=>{r&&B(r,c)},[r]),w=u.useCallback(c=>{r&&$(r,c)},[r]);return{trackProject:m,trackFileDownload:i,trackCodeCopyEvent:x,trackImageExpansion:a,trackScrollDepth:p,trackReadingTime:w}},K=r=>{const{trackProject:e,trackScrollDepth:m,trackReadingTime:i}=j(r);u.useEffect(()=>{if(!r)return;let x=Date.now(),a=0,p;const w=()=>{const g=Math.round(window.scrollY/(document.documentElement.scrollHeight-window.innerHeight)*100);a=Math.max(a,g),clearTimeout(p),p=setTimeout(()=>{m(a)},1e3)},c=()=>{const g=Math.round((Date.now()-x)/1e3);e({viewDuration:g,scrollDepth:a}),i(g)};return window.addEventListener("scroll",w,{passive:!0}),window.addEventListener("beforeunload",c),e(),()=>{window.removeEventListener("scroll",w),window.removeEventListener("beforeunload",c),clearTimeout(p);const g=Math.round((Date.now()-x)/1e3);e({viewDuration:g,scrollDepth:a})}},[r,e,m,i])},X=r=>{const{trackFileDownload:e}=j(r);return{trackDownloadClick:u.useCallback(i=>{e(i),console.log(`📥 Download tracked: ${i}`)},[e])}},J=r=>{const{trackCodeCopyEvent:e}=j(r);return{trackCopyClick:u.useCallback(i=>{e(i),console.log(`📋 Code copy tracked: ${i}`)},[e])}},Z=()=>{const{id:r}=R(),e=_.find(n=>n.id===r),[m,i]=u.useState(!1),x=u.useRef(null);K(e==null?void 0:e.id);const{trackDownloadClick:a}=X(e==null?void 0:e.id),{trackCopyClick:p}=J(e==null?void 0:e.id),c={"monte-carlo":[{id:"abstract",title:"Foundation & Abstract",level:1},{id:"methodology",title:"Abstract Details",level:2},{id:"simulation",title:"Statistical Significance",level:1},{id:"results",title:"Rationale & Methodology",level:1},{id:"conclusion",title:"Future Implementation",level:1},{id:"python-part1",title:"Python Code: Part 1",level:1},{id:"python-part2",title:"Python Code: Part 2",level:1},{id:"python-part3",title:"Python Code: Part 3",level:1}],BondTracker:[{id:"overview",title:"Overview",level:1},{id:"features",title:"Features",level:1},{id:"implementation",title:"Implementation",level:1},{id:"results",title:"Results",level:1}]}[r]||[],g=n=>n.replace(/<div class="expandable-image" data-src="([^"]*)" data-alt="([^"]*)"><\/div>/g,(s,d,o)=>`<div class="expandable-image-placeholder" data-src="${d}" data-alt="${o}"></div>`);u.useEffect(()=>{const n=()=>{const s=window.scrollY||document.documentElement.scrollTop,d=document.documentElement.scrollHeight,o=window.innerHeight;i(s+o>=d*.75)};return window.addEventListener("scroll",n),()=>window.removeEventListener("scroll",n)},[]),u.useEffect(()=>{window.copyToClipboard=o=>{const l=document.getElementById(o);if(l){const v=l.textContent||"";navigator.clipboard.writeText(v).then(()=>{var y;p(o);const h=(y=l.parentElement)==null?void 0:y.querySelector("button");if(h){const S=h.innerHTML;h.innerHTML='<i class="fas fa-check mr-1"></i>Copied!',h.classList.add("bg-green-600"),setTimeout(()=>{h.innerHTML=S,h.classList.remove("bg-green-600")},2e3)}}).catch(()=>{const h=document.createElement("textarea");h.value=v,document.body.appendChild(h),h.select(),document.execCommand("copy"),document.body.removeChild(h)})}};const n=()=>{document.querySelectorAll(".scroll-reveal-image").forEach((l,v)=>{const h=l.getAttribute("data-direction")||"up",y=parseFloat(l.getAttribute("data-delay")||"0");l.classList.add("scroll-reveal-element"),l.style.opacity="0",l.style.transform=s(h),l.style.transition="all 0.8s cubic-bezier(0.25, 0.1, 0.25, 1)",l.style.transitionDelay=`${y+.2}s`})},s=o=>{switch(o){case"left":return"translateX(-30px)";case"right":return"translateX(30px)";case"up":return"translateY(30px)";case"down":return"translateY(-30px)";case"scale":return"scale(0.9)";default:return"translateY(30px)"}},d=setTimeout(()=>{n();const o=new IntersectionObserver(l=>{l.forEach(v=>{if(v.isIntersecting){const h=v.target;h.style.opacity="1",h.style.transform="translateX(0) translateY(0) scale(1)",o.unobserve(h)}})},{threshold:.2,rootMargin:"100px"});return document.querySelectorAll(".scroll-reveal-element").forEach(l=>{o.observe(l)}),()=>o.disconnect()},100);return()=>{delete window.copyToClipboard,clearTimeout(d)}},[e]),u.useEffect(()=>{x.current&&x.current.querySelectorAll(".expandable-image-placeholder").forEach(s=>{const d=s.getAttribute("data-src"),o=s.getAttribute("data-alt");if(d&&o){const l=document.createElement("div");l.className="expandable-image-container",l.innerHTML=`
            <div class="relative rounded-lg overflow-hidden shadow-md cursor-pointer group transition-transform duration-200 hover:scale-105">
              <div class="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all duration-300 z-10 flex items-center justify-center">
                <div class="opacity-0 group-hover:opacity-100 bg-white/90 dark:bg-black/90 rounded-full p-3 shadow-lg transition-opacity duration-300">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-gray-800 dark:text-gray-200">
                    <polyline points="15,3 21,3 21,9" />
                    <polyline points="9,21 3,21 3,15" />
                    <line x1="21" y1="3" x2="14" y2="10" />
                    <line x1="3" y1="21" x2="10" y2="14" />
                  </svg>
                </div>
              </div>

              <img src="${d}" alt="${o}" class="w-full h-auto object-cover transition-transform duration-300 group-hover:scale-105" />
            </div>
          `,l.addEventListener("click",()=>{b(d,o)}),s.replaceWith(l)}})},[e]);const b=(n,s)=>{const d=document.createElement("div");d.className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/80 backdrop-blur-sm",d.innerHTML=`
      <div class="relative max-w-[95vw] max-h-[95vh] bg-card rounded-lg shadow-2xl overflow-hidden">
        <button class="absolute top-4 right-4 z-10 p-2 bg-black/50 text-white rounded-full hover:bg-black/70 transition-colors" onclick="this.closest('.fixed').remove(); document.body.style.overflow = 'auto';">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="18" y1="6" x2="6" y2="18" />
            <line x1="6" y1="6" x2="18" y2="18" />
          </svg>
        </button>
        <img src="${n}" alt="${s}" class="w-full h-full object-contain" style="max-width: 95vw; max-height: 95vh;" />
        <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4">
          <p class="text-white text-sm font-medium">${s}</p>
        </div>
      </div>
    `,d.addEventListener("click",l=>{l.target===d&&(d.remove(),document.body.style.overflow="auto")});const o=l=>{l.key==="Escape"&&(d.remove(),document.body.style.overflow="auto",document.removeEventListener("keydown",o))};document.addEventListener("keydown",o),document.body.style.overflow="hidden",document.body.appendChild(d)},f=()=>{window.scrollTo({top:0,behavior:"smooth"})};return e?t.jsxs(t.Fragment,{children:[t.jsx(U,{}),t.jsx(q,{sections:c}),t.jsxs(E.main,{id:"top",initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:.5,ease:"easeOut"},className:"py-20 px-6 max-w-7xl mx-auto relative",children:[t.jsxs("div",{className:"mb-8 flex flex-col sm:flex-row gap-4 justify-between items-start",children:[t.jsx(C,{to:"/projects",className:"inline-block text-primary text-md border border-transparent hover:border-primary px-4 py-2 rounded transition",children:"← Back to Projects"}),t.jsx(C,{to:"/",className:"inline-block text-primary text-md border border-transparent hover:border-primary px-4 py-2 rounded transition",children:"← Back to Home"})]}),t.jsxs("div",{className:"mb-10",children:[t.jsx("h1",{className:"text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-serif font-bold mb-4 text-center",children:e.title}),t.jsxs("div",{className:"flex flex-col sm:flex-row gap-4 justify-center items-center mb-6",children:[t.jsx(Y,{projectId:e.id,recordView:!0}),t.jsx(W,{})]}),t.jsx("div",{className:"flex gap-2 mb-6 flex-wrap justify-center",children:e.tags.map((n,s)=>t.jsx("span",{className:"px-4 py-2 bg-secondary rounded-full text-sm text-primary",children:n},s))}),t.jsxs("div",{className:"relative overflow-hidden rounded-lg mb-8 shadow-lg max-w-4xl mx-auto",children:[t.jsx("img",{src:e.image,alt:e.title,className:"w-full h-64 md:h-80 lg:h-96 object-cover transition-transform duration-700 hover:scale-105"}),t.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-[hsl(var(--navy-dark))] to-transparent opacity-40"})]}),t.jsx("p",{className:"text-xl text-muted-foreground text-center max-w-4xl mx-auto leading-relaxed",children:e.description})]}),t.jsx("div",{ref:x,className:"prose dark:prose-invert max-w-none text-[1.20rem] leading-relaxed",dangerouslySetInnerHTML:{__html:g(e.content)}}),e.pdfUrl&&t.jsxs("div",{className:"mt-12 p-6 bg-card rounded-lg border border-primary/20 max-w-4xl mx-auto",children:[t.jsx("h3",{className:"text-xl font-medium mb-4 text-center",children:"Project Resources"}),t.jsxs("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[t.jsxs("a",{href:e.pdfUrl,target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center justify-center px-6 py-3 bg-primary/10 border border-primary/30 rounded-md text-primary hover:bg-primary/20 transition-colors shadow-md",children:[t.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"mr-2",children:[t.jsx("path",{d:"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"}),t.jsx("polyline",{points:"14,2 14,8 20,8"}),t.jsx("line",{x1:"16",y1:"13",x2:"8",y2:"13"}),t.jsx("line",{x1:"16",y1:"17",x2:"8",y2:"17"}),t.jsx("polyline",{points:"10,9 9,9 8,9"})]}),"View PDF"]}),t.jsxs("a",{href:e.pdfUrl,download:!0,onClick:()=>{var n;return a(((n=e.pdfUrl)==null?void 0:n.split("/").pop())||"project.pdf")},className:"inline-flex items-center justify-center px-6 py-3 bg-secondary border border-primary/30 rounded-md text-primary hover:bg-secondary/80 transition-colors shadow-md",children:[t.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"mr-2",children:[t.jsx("path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"}),t.jsx("polyline",{points:"7,10 12,15 17,10"}),t.jsx("line",{x1:"12",y1:"15",x2:"12",y2:"3"})]}),"Download PDF"]})]})]}),t.jsx("div",{className:"max-w-4xl mx-auto",children:t.jsx(G,{repo:"Jiffatron/Brayden-Academic-Website2",repoId:"R_kgDOOQeojA",category:"General",categoryId:"DIC_kwDOOQeojM4CsRa9",mapping:"pathname",strict:"0",reactionsEnabled:"1",emitMetadata:"0",inputPosition:"bottom",theme:"preferred_color_scheme",lang:"en"})})]}),t.jsx(H,{children:m&&t.jsx(E.button,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.8},onClick:f,className:"fixed bottom-8 right-8 p-3 bg-primary text-primary-foreground rounded-full shadow-lg hover:bg-primary/90 transition-colors z-50","aria-label":"Scroll to top",children:t.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[t.jsx("line",{x1:"12",y1:"19",x2:"12",y2:"5"}),t.jsx("polyline",{points:"5,12 12,5 19,12"})]})})})]}):t.jsxs("div",{className:"py-20 px-6 max-w-5xl mx-auto",children:[t.jsx("h1",{className:"text-3xl font-bold",children:"Project not found"}),t.jsx(C,{to:"/projects",className:"inline-block mt-4 text-primary text-md border border-transparent hover:border-primary px-4 py-2 rounded transition",children:"← Back to Projects"})]})};export{Z as default};
