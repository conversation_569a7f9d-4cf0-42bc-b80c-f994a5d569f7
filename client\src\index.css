@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Inter:wght@300;400;500;600;700&family=Space+Grotesk:wght@300;400;500;600;700&family=Sora:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Light mode variables */
    --background: 0 0% 98%;
    --foreground: 222 47% 11%;
    
    --card: 0 0% 100%;
    --card-foreground: 222 47% 11%;
    
    --popover: 0 0% 100%;
    --popover-foreground: 222 47% 11%;
    
    --primary: 189 98% 64%;
    --primary-foreground: 219 67% 10%;
    
    --secondary: 210 40% 96%;
    --secondary-foreground: 222 47% 11%;
    
    --muted: 210 40% 96%;
    --muted-foreground: 215 16% 47%;
    
    --accent: 210 40% 96%;
    --accent-foreground: 222 47% 11%;
    
    --destructive: 0 84% 60%;
    --destructive-foreground: 210 40% 98%;
    
    --border: 214 32% 91%;
    --input: 214 32% 91%;
    --ring: 222 47% 11%;
    
    --radius: 0.5rem;

    /* Custom theme colors */
    --navy-light: 217 51% 18%;
    --navy: 216 56% 12%;
    --navy-dark: 219 67% 5%;
    --cyan: 189 98% 64%;
    --cyan-dark: 189 80% 50%;
    --gold: 45 90% 55%;
    --gold-light: 45 90% 75%;
    --charcoal: 220 14% 15%;
    --slate-light: 217 100% 95%;
    --slate: 216 32% 63%;
    --slate-dark: 217 26% 36%;
    --ivory: 60 30% 96%;
  }
  
  .dark {
    /* Dark mode variables */
    --background: 219 67% 5%;
    --foreground: 217 100% 95%;
    
    --card: 220 14% 10%;
    --card-foreground: 217 100% 95%;
    
    --popover: 220 14% 10%;
    --popover-foreground: 217 100% 95%;
    
    --primary: 189 98% 64%;
    --primary-foreground: 219 67% 5%;
    
    --secondary: 217 26% 20%;
    --secondary-foreground: 217 100% 95%;
    
    --muted: 217 26% 15%;
    --muted-foreground: 216 32% 70%;
    
    --accent: 220 14% 15%;
    --accent-foreground: 217 100% 95%;
    
    --destructive: 0 84% 60%;
    --destructive-foreground: 217 100% 95%;
    
    --border: 217 26% 20%;
    --input: 217 26% 20%;
    --ring: 189 98% 64%;
  }

  * {
    @apply border-border;
  }

  html {
    scroll-behavior: smooth;
  }

  body {
    @apply bg-background text-foreground antialiased transition-colors duration-300;
    font-family: 'Inter', 'Space Grotesk', sans-serif;
  }

  /* Custom Typography */
  h1, h2, h3, h4, h5, h6 {
    font-family: 'Playfair Display', 'Sora', serif;
  }
}

@layer components {
  .section {
    @apply py-24 px-6 md:px-16 lg:px-24 opacity-0;
  }

  .section-visible {
    @apply opacity-100 transition-opacity duration-700 ease-in-out;
  }

  /* Standardized Section Headers */
  .section-title {
    @apply text-3xl md:text-4xl font-serif font-bold mb-12 relative inline-block;
  }

  .section-title::after {
    @apply content-[''] absolute -bottom-2 left-0 w-1/2 h-px bg-primary;
  }

  /* Standardized Section Text */
  .section-text {
    @apply text-lg leading-relaxed;
  }

  .section-description {
    @apply text-lg leading-relaxed mb-12 max-w-3xl;
  }

  /* Python Code Block Styling */
  .python-code-block {
    @apply bg-gray-900 text-green-400 p-6 rounded-lg overflow-x-auto font-mono text-sm leading-relaxed shadow-lg border border-gray-700;
  }

  .python-code-block pre {
    @apply m-0 whitespace-pre-wrap;
  }

  .python-code-block .keyword {
    @apply text-blue-600 dark:text-blue-400 font-semibold;
  }

  .python-code-block .string {
    @apply text-red-600 dark:text-yellow-300;
  }

  .python-code-block .comment {
    @apply text-green-600 dark:text-gray-500 italic;
  }

  .python-code-block .function {
    @apply text-yellow-600 dark:text-purple-400;
  }

  .python-code-block .number {
    @apply text-purple-600 dark:text-orange-400;
  }

  /* Expandable Image Styling */
  .expandable-image-container {
    @apply relative rounded-lg overflow-hidden shadow-md cursor-pointer;
  }

  .expandable-image-container:hover {
    transform: scale(1.02);
    transition: transform 0.2s ease-out;
  }

  .navy-dark-section {
    @apply bg-secondary/30 dark:bg-[hsl(var(--navy-dark))];
  }

  .nav-link {
    @apply relative text-sm tracking-wide hover:text-primary transition-colors;
  }

  .nav-link::after {
    @apply content-[''] absolute bottom-[-5px] left-0 w-0 h-[2px] bg-primary transition-all duration-300;
  }

  .nav-link:hover::after {
    @apply w-full;
  }

  /* Glassmorphism Elements */
  .glass {
    @apply bg-card/40 backdrop-blur-lg border border-primary/10 shadow-xl;
  }

  .glass-card {
    @apply bg-card/60 backdrop-blur-md border border-border/20 rounded-lg shadow-lg;
  }

  .glass-nav {
    @apply bg-background/70 backdrop-blur-lg border-b border-primary/10 shadow-sm;
  }

  /* Mobile-First Design System */
  @media (max-width: 768px) {
    /* Root font size for mobile - 16px base */
    html {
      font-size: 16px;
    }

    /* Safe area support for iPhone */
    .safe-area-pb {
      padding-bottom: env(safe-area-inset-bottom);
    }

    .h-safe-area-inset-bottom {
      height: env(safe-area-inset-bottom);
    }

    /* Adjust hero for mobile bottom nav */
    #hero {
      padding-bottom: calc(3rem + env(safe-area-inset-bottom) + 80px);
    }

    /* Mobile typography scale */
    h1 { font-size: 2rem; line-height: 1.2; margin-bottom: 1rem; }
    h2 { font-size: 1.75rem; line-height: 1.3; margin-bottom: 0.875rem; }
    h3 { font-size: 1.5rem; line-height: 1.3; margin-bottom: 0.75rem; }
    h4 { font-size: 1.25rem; line-height: 1.4; margin-bottom: 0.625rem; }
    h5 { font-size: 1.125rem; line-height: 1.4; margin-bottom: 0.5rem; }
    h6 { font-size: 1rem; line-height: 1.4; margin-bottom: 0.5rem; }

    p, li {
      font-size: 1rem;
      line-height: 1.4;
      margin-bottom: 1rem;
    }

    /* Mobile spacing system - generous margins */
    .mobile-container {
      padding-left: 1.5rem; /* 24px */
      padding-right: 1.5rem; /* 24px */
    }

    .mobile-section {
      padding-top: 3rem; /* 48px */
      padding-bottom: 3rem; /* 48px */
    }

    /* Touch targets - 44px minimum */
    * {
      touch-action: manipulation;
    }

    button, a, [role="button"] {
      touch-action: manipulation;
      -webkit-tap-highlight-color: transparent;
      min-height: 44px; /* Apple HIG minimum */
      min-width: 44px;
      padding: 0.75rem 1rem; /* Generous padding */
    }

    .glass-nav {
      pointer-events: auto;
    }

    /* Ensure mobile menu doesn't interfere when closed */
    .mobile-menu-closed {
      pointer-events: none !important;
      visibility: hidden;
    }

    /* Fix for invisible blocking elements */
    .fixed, .absolute {
      pointer-events: none;
    }

    /* Re-enable pointer events for interactive elements */
    .fixed button, .fixed a, .fixed [role="button"],
    .absolute button, .absolute a, .absolute [role="button"] {
      pointer-events: auto;
    }

    /* Ensure navbar is clickable */
    nav, .navbar, [role="navigation"] {
      pointer-events: auto !important;
    }

    /* Ensure main content is clickable */
    main, .main-content, article, section {
      pointer-events: auto !important;
    }

    /* Fix for any overlay issues */
    .backdrop-blur-sm, .backdrop-blur-md, .backdrop-blur-lg {
      pointer-events: none;
    }

    /* Re-enable for modal content */
    .modal-content, .dialog-content {
      pointer-events: auto;
    }

    /* Ensure project cards are clickable */
    .project-card, .mobile-project-card {
      pointer-events: auto !important;
    }

    /* Prevent layout shifts on mobile project cards */
    .mobile-project-card {
      contain: layout style;
      will-change: transform;
    }

    /* Stable image containers */
    .project-image-container {
      aspect-ratio: 16/9;
      background-color: rgba(var(--muted-foreground), 0.1);
    }

    /* Prevent content jumping during image load */
    .project-card img {
      display: block;
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    /* Mobile-specific improvements - only apply on mobile */
    .mobile-project-card {
      margin-bottom: 1.5rem;
      border-radius: 1rem;
      overflow: hidden;
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .mobile-project-card:hover {
      transform: translateY(-4px);
      box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
    }

    /* Keep original blog card styles for desktop */
    .blog-card {
      margin-bottom: 1.5rem;
      padding: 1.5rem;
    }

    /* Instagram-style image overlays */
    .line-clamp-2 {
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .line-clamp-3 {
      display: -webkit-box;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    /* Better mobile form styling */
    input, textarea, select {
      font-size: 16px; /* Prevents zoom on iOS */
      padding: 1rem;
      border-radius: 0.5rem;
    }

    /* Mobile-friendly images */
    img {
      max-width: 100%;
      height: auto;
    }

    /* Improved mobile readability */
    .section-description {
      font-size: 1.125rem;
      line-height: 1.6;
      margin-bottom: 2rem;
    }

    /* Mobile grid improvements */
    .grid {
      gap: 1.5rem;
    }

    /* Better mobile spacing for cards */
    .card, .project-card, .blog-card {
      border-radius: 1rem;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    /* Mobile resume improvements - LinkedIn/Instagram style */
    #resume {
      padding-bottom: calc(6rem + env(safe-area-inset-bottom) + 80px);
    }

    /* Mobile resume cards - Apple HIG compliant */
    .mobile-resume-card {
      min-height: 44px; /* Apple HIG minimum touch target */
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      backdrop-filter: blur(10px);
    }

    .mobile-resume-card:hover {
      transform: scale(1.02);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }

    /* Mobile resume typography - optimized for readability */
    .mobile-resume-text {
      font-size: 14px;
      line-height: 1.5;
      letter-spacing: 0.01em;
    }

    /* Mobile resume section headers */
    .mobile-section-header {
      font-size: 18px;
      font-weight: 600;
      line-height: 1.3;
    }

    /* Mobile bullet points - better touch targets */
    .mobile-bullet {
      min-height: 24px;
      display: flex;
      align-items: flex-start;
      gap: 12px;
    }

    /* Scroll-friendly spacing */
    .mobile-resume-spacing {
      margin-bottom: 24px;
    }

    /* Bottom nav safe area */
    .mobile-content-bottom {
      padding-bottom: calc(80px + env(safe-area-inset-bottom) + 2rem);
    }

    /* Hero image improvements - prevent glossy/wet appearance */
    .hero-image {
      image-rendering: -webkit-optimize-contrast;
      image-rendering: crisp-edges;
      backface-visibility: hidden;
      transform: translateZ(0);
    }

    /* Remove any glossy effects from shadows */
    .natural-shadow {
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15), 0 4px 10px rgba(0, 0, 0, 0.1);
    }

    .natural-shadow:hover {
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2), 0 8px 16px rgba(0, 0, 0, 0.15);
    }
  }

  /* Smaller mobile devices */
  @media (max-width: 480px) {
    .mobile-container {
      padding-left: 1rem; /* 16px */
      padding-right: 1rem; /* 16px */
    }

    h1 { font-size: 1.875rem; } /* 30px */
    h2 { font-size: 1.5rem; }   /* 24px */
    h3 { font-size: 1.25rem; }  /* 20px */

    .mobile-section {
      padding-top: 2rem; /* 32px */
      padding-bottom: 2rem; /* 32px */
    }
  }

  /* Respect user's motion preferences */
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
      scroll-behavior: auto !important;
    }

    /* Disable transform animations */
    .fade-in,
    .slide-in,
    .scale-in {
      animation: none !important;
      transform: none !important;
    }

    /* Disable hover effects that use transform */
    .project-card:hover,
    .blog-card:hover {
      transform: none !important;
    }
  }

  /* Performance optimizations for animations */
  .animate-gpu {
    transform: translateZ(0);
    will-change: transform, opacity;
  }

  /* Smooth animations for users who prefer motion */
  @media (prefers-reduced-motion: no-preference) {
    .smooth-animation {
      transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                  opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }
  }

  /* Scroll reveal animations for Monte Carlo images */
  .scroll-reveal-element {
    transform: translateZ(0); /* GPU acceleration */
    will-change: transform, opacity;
  }

  .monte-carlo-images {
    perspective: 1000px; /* Add depth for 3D transforms */
  }

  .monte-carlo-images .expandable-image {
    transition: transform 1.0s cubic-bezier(0.25, 0.1, 0.25, 1);
    cursor: pointer;
  }

  .monte-carlo-images .expandable-image:hover {
    transform: scale(1.02) translateZ(10px);
  }

  /* Much slower, more luxurious expansion animation for Monte Carlo images */
  .monte-carlo-images .expandable-image img {
    transition: transform 1.2s cubic-bezier(0.25, 0.1, 0.25, 1),
                box-shadow 1.2s cubic-bezier(0.25, 0.1, 0.25, 1);
  }

  .monte-carlo-images .expandable-image:hover img {
    transform: scale(1.05);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  }

  /* Enhanced project card animations */
  .project-card {
    transition: transform 0.8s cubic-bezier(0.25, 0.1, 0.25, 1),
                box-shadow 0.8s cubic-bezier(0.25, 0.1, 0.25, 1);
  }

  .project-card:hover {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
                0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  /* Image modal animations */
  .image-modal img {
    transition: transform 0.8s cubic-bezier(0.25, 0.1, 0.25, 1);
  }

  /* Refined expandable image animations */
  .expandable-image {
    overflow: hidden;
    border-radius: 0.5rem;
  }

  .expandable-image img {
    transition: transform 1.0s cubic-bezier(0.25, 0.1, 0.25, 1),
                filter 1.0s cubic-bezier(0.25, 0.1, 0.25, 1);
    will-change: transform;
  }

  .expandable-image:hover img {
    transform: scale(1.05);
    filter: brightness(1.1);
  }

  /* Smooth scroll behavior for better UX */
  @media (prefers-reduced-motion: no-preference) {
    html {
      scroll-behavior: smooth;
    }
  }

  /* Giscus Comments Styling */
  .giscus-comments {
    margin-top: 2rem;
    padding-top: 2rem;
  }

  .giscus-container {
    border-radius: 0.5rem;
    overflow: hidden;
  }

  /* Ensure Giscus iframe respects our theme */
  .giscus-frame {
    border-radius: 0.5rem;
    border: 1px solid hsl(var(--border));
  }

  /* Dark mode adjustments for Giscus */
  .dark .giscus-frame {
    border-color: hsl(var(--border));
  }

  /* 3D/Depth Effects */
  .card-3d {
    @apply relative shadow-[0_10px_30px_rgba(0,0,0,0.15)] transition-all duration-300 hover:shadow-[0_15px_40px_rgba(0,0,0,0.2)];
  }

  .depth-effect {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1), 
                0 15px 40px rgba(0, 0, 0, 0.1);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  }

  .depth-effect:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2),
                0 20px 48px rgba(0, 0, 0, 0.15);
  }

  /* Headshot and Profile Elements */
  .headshot-container {
    @apply relative overflow-hidden rounded-lg;
  }

  .headshot-container::before {
    @apply content-[''] absolute top-5 left-5 w-full h-full border-2 border-primary z-[-1] transition-transform duration-300;
  }

  .headshot-container:hover::before {
    @apply transform -translate-x-[10px] -translate-y-[10px];
  }

  /* Cards and Interactive Elements */
  .project-card {
    @apply transition-all duration-300 bg-card/70 backdrop-blur-sm rounded-lg overflow-hidden border border-white/5;
  }

  .project-card:hover {
    @apply transform -translate-y-[5px] shadow-lg border-primary/20;
  }

  .blog-card {
    @apply transition-transform duration-300 border-l-2 border-transparent;
  }

  .blog-card:hover {
    @apply transform translate-x-2 border-l-2 border-primary;
  }

  /* CV and Timeline Elements */
  .cv-section {
    @apply relative;
  }

  .cv-section::before {
    @apply content-[''] absolute left-0 top-3 h-10 w-0.5 bg-primary;
  }

  .timeline-item {
    @apply relative;
  }

  .timeline-item::before {
    @apply content-[''] absolute left-[-25px] top-2 h-2 w-2 rounded-full bg-primary;
  }

  .timeline-item::after {
    @apply content-[''] absolute left-[-22px] top-5 h-[calc(100%+20px)] w-0.5 bg-primary/30;
  }

  .timeline-item:last-child::after {
    @apply hidden;
  }
  
  /* Animations */
  .fade-in {
    animation: fadeIn 0.8s ease-out forwards;
  }
  
  .slide-up {
    animation: slideUp 0.6s ease-out forwards;
  }
  
  .slide-in-right {
    animation: slideInRight 0.6s ease-out forwards;
  }
  
  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }
  
  @keyframes slideUp {
    from { 
      opacity: 0;
      transform: translateY(30px);
    }
    to { 
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  @keyframes slideInRight {
    from { 
      opacity: 0;
      transform: translateX(30px);
    }
    to { 
      opacity: 1;
      transform: translateX(0);
    }
  }
  
  /* Last Updated Timestamp */
  .timestamp {
    @apply text-xs text-muted-foreground/70 pt-4 text-center;
  }
}
