{"name": "brayden-academic-site", "version": "1.0.0", "type": "module", "license": "MIT", "homepage": "https://jiffatron.github.io/Brayden-Academic-Website2", "scripts": {"dev": "vite", "build": "vite build && node copy-cname.js", "predeploy": "npm run build", "deploy": "gh-pages -d docs"}, "dependencies": {"@emailjs/browser": "^4.4.1", "@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-context-menu": "^2.2.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-hover-card": "^1.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.2", "@radix-ui/react-navigation-menu": "^1.2.1", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.3", "@tanstack/react-query": "^5.60.5", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "cors": "^2.8.5", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "drizzle-orm": "^0.39.1", "drizzle-zod": "^0.7.0", "embla-carousel-react": "^8.3.0", "express": "^5.1.0", "framer-motion": "^11.13.1", "input-otp": "^1.2.4", "lucide-react": "^0.453.0", "nodemailer": "^7.0.3", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.53.1", "react-icons": "^5.4.0", "react-resizable-panels": "^2.1.4", "react-router-dom": "^7.6.0", "recharts": "^2.13.0", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.0", "wouter": "^3.3.5", "zod": "^3.23.8", "zod-validation-error": "^3.4.0"}, "devDependencies": {"@tailwindcss/typography": "^0.5.15", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.2", "autoprefixer": "^10.4.20", "esbuild": "^0.25.0", "gh-pages": "^6.0.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.14", "typescript": "5.6.3", "vite": "^5.4.14"}, "overrides": {"esbuild": "^0.25.0"}, "main": "copy-cname.js", "directories": {"doc": "docs"}, "repository": {"type": "git", "url": "git+https://github.com/Jiffatron/Brayden-Academic-Website2.git"}, "keywords": [], "author": "", "bugs": {"url": "https://github.com/Jiffatron/Brayden-Academic-Website2/issues"}, "description": ""}