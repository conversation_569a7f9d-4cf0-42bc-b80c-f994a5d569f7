import{j as e,m as s,L as t,b as i}from"./index-DmbGcuU4.js";const l=()=>e.jsxs(s.main,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:.5,ease:"easeOut"},className:"py-20 px-6 max-w-6xl mx-auto",children:[e.jsx("h1",{className:"text-3xl font-serif font-bold mb-2",children:"All Blog Posts"}),e.jsx(t,{to:"/",className:"inline-block mb-10 text-primary text-md border border-transparent hover:border-primary px-4 py-2 rounded transition",children:"← Back to Home"}),e.jsx("div",{className:"grid gap-8",children:i.map((a,r)=>e.jsxs("div",{className:"blog-card border-b pb-4",children:[e.jsx("p",{className:"text-primary text-sm",children:a.date}),e.jsx("h2",{className:"text-2xl font-semibold mb-2",children:a.title}),e.jsx("p",{className:"text-muted-foreground mb-3",children:a.preview}),e.jsx(t,{to:`/blog/${a.slug}`,className:"text-primary text-sm hover:text-primary/80",children:"Read More →"})]},r))})]});export{l as default};
