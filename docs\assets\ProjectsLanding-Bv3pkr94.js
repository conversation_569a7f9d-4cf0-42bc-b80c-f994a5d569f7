import{a as d,j as a,m as i,L as c,p as m}from"./index-DmbGcuU4.js";const p=()=>{const s=d(),t=e=>{s(`/projects/${e}`)},r={hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1,delayChildren:.2}}},n={hidden:{y:20,opacity:0},visible:{y:0,opacity:1,transition:{duration:.5,ease:"easeOut"}}};return a.jsx(a.Fragment,{children:a.jsxs(i.main,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:.5,ease:"easeOut"},className:"py-20 px-6 max-w-6xl mx-auto",children:[a.jsx("h1",{className:"text-3xl font-serif font-bold mb-2",children:"All Projects"}),a.jsx(c,{to:"/",className:"inline-block mb-10 text-primary text-md border border-transparent hover:border-primary px-4 py-2 rounded transition",children:"← Back to Home"}),a.jsx(i.div,{className:"grid md:grid-cols-2 gap-8",initial:"hidden",animate:"visible",variants:r,children:m.map(e=>a.jsxs(i.div,{className:"bg-card rounded-lg overflow-hidden shadow-md project-card cursor-pointer",variants:n,whileHover:{y:-5},onClick:()=>t(e.id),children:[a.jsx("img",{src:e.image,alt:e.title,className:"w-full h-48 object-cover"}),a.jsxs("div",{className:"p-6",children:[a.jsx("h3",{className:"text-xl font-serif font-semibold mb-3",children:e.title}),a.jsx("p",{className:"text-muted-foreground mb-4",children:e.description}),a.jsx("div",{className:"flex gap-2 mb-4 flex-wrap",children:e.tags.map((l,o)=>a.jsx("span",{className:"px-3 py-1 bg-secondary rounded-full text-xs text-primary",children:l},o))}),a.jsxs("div",{className:"text-primary flex items-center text-sm hover:text-primary/80 transition-colors",children:[a.jsx("span",{children:"View Details"}),a.jsx("i",{className:"fas fa-arrow-right ml-2"})]})]})]},e.id))})]})})};export{p as default};
